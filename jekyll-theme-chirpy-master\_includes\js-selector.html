<!-- JS selector for site. -->

<!-- commons -->

{% assign urls = site.data.origin[type].search.js %}

<!-- layout specified -->

{% if page.layout == 'post' or page.layout == 'page' or page.layout == 'home' %}
  {% assign urls = urls | append: ',' | append: site.data.origin[type]['lazy-polyfill'].js %}

  {% unless page.layout == 'home' %}
    <!-- image lazy-loading & popup & clipboard -->
    {% assign urls = urls
      | append: ','
      | append: site.data.origin[type].glightbox.js
      | append: ','
      | append: site.data.origin[type].clipboard.js
    %}
  {% endunless %}
{% endif %}

{% if page.layout == 'home'
  or page.layout == 'post'
  or page.layout == 'archives'
  or page.layout == 'category'
  or page.layout == 'tag'
%}
  {% assign locale = include.lang | split: '-' | first %}

  {% assign urls = urls
    | append: ','
    | append: site.data.origin[type].dayjs.js.common
    | append: ','
    | append: site.data.origin[type].dayjs.js.locale
    | replace: ':LOCALE', locale
    | append: ','
    | append: site.data.origin[type].dayjs.js.relativeTime
    | append: ','
    | append: site.data.origin[type].dayjs.js.localizedFormat
  %}
{% endif %}

{% if page.content contains '<h2' or page.content contains '<h3' and site.toc and page.toc %}
  {% assign urls = urls | append: ',' | append: site.data.origin[type].toc.js %}
{% endif %}

{% if page.mermaid %}
  {% assign urls = urls | append: ',' | append: site.data.origin[type].mermaid.js %}
{% endif %}

{% include jsdelivr-combine.html urls=urls %}

{% case page.layout %}
  {% when 'home', 'categories', 'post', 'page' %}
    {% assign js = page.layout %}
  {% when 'archives', 'category', 'tag' %}
    {% assign js = 'misc' %}
  {% else %}
    {% assign js = 'commons' %}
{% endcase %}

{% capture script %}/assets/js/dist/{{ js }}.min.js{% endcapture %}

<script defer src="{{ script | relative_url }}"></script>

{% if page.math %}
  <!-- MathJax -->
  <script src="{{ '/assets/js/data/mathjax.js' | relative_url }}"></script>
  <script async src="https://cdnjs.cloudflare.com/polyfill/v3/polyfill.min.js?features=es6"></script>
  <script id="MathJax-script" async src="{{ site.data.origin[type].mathjax.js | relative_url }}"></script>
{% endif %}

<!-- Pageviews -->
{% if page.layout == 'post' %}
  {% assign provider = site.pageviews.provider %}

  {% if provider and provider != empty %}
    {% case provider %}
      {% when 'goatcounter' %}
        {% if site.analytics[provider].id != empty and site.analytics[provider].id %}
          {% include pageviews/{{ provider }}.html %}
        {% endif %}
    {% endcase %}
  {% endif %}
{% endif %}
