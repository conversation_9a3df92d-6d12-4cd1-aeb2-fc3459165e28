name: Feature Request
description: Suggest an idea for this project
labels:
  - enhancement
body:
  - type: checkboxes
    attributes:
      label: Checklist
      description: Following the guidelines can make you more likely to get responses.
      options:
        - label: >-
            I have read and accepted the
            [contributing guidelines](https://github.com/cotes2020/jekyll-theme-chirpy/blob/master/docs/CONTRIBUTING.md).
          required: true

  - type: textarea
    attributes:
      label: Is your feature request related to a problem? Please describe
      description: A clear and concise description of what the problem is.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Describe the solution you'd like
      description: A clear and concise description of what you want to happen.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Describe alternatives you've considered
      description: A clear and concise description of any alternative solutions or features you've considered.

  - type: textarea
    attributes:
      label: Additional context
      description: Add any other context or screenshots about the feature request here.
