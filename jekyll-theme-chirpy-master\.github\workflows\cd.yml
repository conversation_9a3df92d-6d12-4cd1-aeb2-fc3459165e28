name: CD

on:
  push:
    branches: [production]
    tags-ignore: ["**"]

jobs:
  release:
    if: ${{ ! startsWith(github.event.head_commit.message, 'chore(release)') }}
    permissions:
      contents: write
      issues: write
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.3
          bundler-cache: true

      - uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - run: npm install
      - run: npx semantic-release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GEM_HOST_API_KEY: ${{ secrets.GEM_HOST_API_KEY }}

  publish:
    needs: release
    uses: ./.github/workflows/publish.yml
    secrets:
      GH_PAT: ${{ secrets.GH_PAT }}
      BUILDER: ${{ secrets.BUILDER }}
