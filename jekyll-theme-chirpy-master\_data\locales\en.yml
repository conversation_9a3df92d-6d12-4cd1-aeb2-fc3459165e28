# The layout text of site

# ----- Commons label -----

layout:
  post: Post
  category: Category
  tag: Tag

# The tabs of sidebar
tabs:
  # format: <filename_without_extension>: <value>
  home: Home
  categories: Categories
  tags: Tags
  archives: Archives
  about: About

# the text displayed in the search bar & search results
search:
  hint: search
  cancel: Cancel
  no_results: Oops! No results found.

panel:
  lastmod: Recently Updated
  trending_tags: Trending Tags
  toc: Contents

copyright:
  # Shown at the bottom of the post
  license:
    template: This post is licensed under :LICENSE_NAME by the author.
    name: CC BY 4.0
    link: https://creativecommons.org/licenses/by/4.0/

  # Displayed in the footer
  brief: Some rights reserved.
  verbose: >-
    Except where otherwise noted, the blog posts on this site are licensed
    under the Creative Commons Attribution 4.0 International (CC BY 4.0) License by the author.

meta: Using the :THEME theme for :PLATFORM.

not_found:
  statement: Sorry, we've misplaced that URL or it's pointing to something that doesn't exist.

notification:
  update_found: A new version of content is available.
  update: Update

# ----- Posts related labels -----

post:
  written_by: By
  posted: Posted
  updated: Updated
  words: words
  pageview_measure: views
  read_time:
    unit: min
    prompt: read
  relate_posts: Further Reading
  share: Share
  button:
    next: Newer
    previous: Older
    copy_code:
      succeed: Copied!
    share_link:
      title: Copy link
      succeed: Link copied successfully!

# Date time format.
# See: <http://strftime.net/>, <https://day.js.org/docs/en/display/format>
df:
  post:
    strftime: "%b %e, %Y"
    dayjs: "ll"
  archives:
    strftime: "%b"
    dayjs: "MMM"

# categories page
categories:
  category_measure:
    singular: category
    plural: categories
  post_measure:
    singular: post
    plural: posts
