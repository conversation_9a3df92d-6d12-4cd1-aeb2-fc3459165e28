{%- comment -%}
  Generate media resource final URL based on `site.cdn`, `page.media_subpath`

  Arguments:
    src - required, basic media resources path
    subpath - optional, relative path of media resources
    absolute - optional, boolean, if true, generate absolute URL

  Return:
    media resources URL
{%- endcomment -%}

{% assign url = include.src %}

{%- if url -%}
  {% unless url contains ':' %}
    {%- comment -%} Add media resources subpath prefix {%- endcomment -%}
    {% assign url = include.subpath | default: '' | append: '/' | append: url %}

    {%- comment -%} Prepend CND URL {%- endcomment -%}
    {% if site.cdn %}
      {% assign url = site.cdn | append: '/' | append: url %}
    {% endif %}

    {% assign url = url | replace: '///', '/' | replace: '//', '/' | replace: ':/', '://' %}

    {% unless url contains '://' %}
      {% if include.absolute %}
        {% assign url = site.url | append: site.baseurl | append: url %}
      {% else %}
        {% assign url = site.baseurl | append: url %}
      {% endif %}
    {% endunless %}
  {% endunless %}
{%- endif -%}

{{- url -}}
