<!-- Display <PERSON>t<PERSON>ounter pageviews -->
<script>
  document.addEventListener('DOMContentLoaded', () => {
    const pv = document.getElementById('pageviews');

    if (pv !== null) {
      const uri = location.pathname.replace(/\/$/, '');
      const url = `https://{{ site.analytics.goatcounter.id }}.goatcounter.com/counter/${encodeURIComponent(uri)}.json`;

      fetch(url)
        .then((response) => response.json())
        .then((data) => {
          const count = data.count.replace(/\D/g, '');
          pv.innerText = new Intl.NumberFormat().format(count);
        })
        .catch((error) => {
          pv.innerText = '1';
        });
    }
  });
</script>
