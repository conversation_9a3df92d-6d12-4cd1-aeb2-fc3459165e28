version: 2
updates:
  - package-ecosystem: "bundler"
    directory: "/"
    schedule:
      interval: "weekly"
  - package-ecosystem: "npm"
    directory: "/"
    versioning-strategy: increase
    groups:
      prod-deps:
        dependency-type: production
      dev-deps:
        dependency-type: development
    schedule:
      interval: "weekly"
  - package-ecosystem: "github-actions"
    directory: "/"
    groups:
      gh-actions:
        update-types:
          - "major"
    schedule:
      interval: "weekly"
  - package-ecosystem: "devcontainers"
    directory: "/"
    schedule:
      interval: weekly
