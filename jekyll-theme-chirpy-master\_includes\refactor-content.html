<!-- Refactor the HTML structure -->

{% assign _content = include.content %}

<!--
  In order to allow a wide table to scroll horizontally,
  we suround the markdown table with `<div class="table-wrapper">` and `</div>`
-->

{% if _content contains '<table' %}
  {% assign _content = _content
    | replace: '<table', '<div class="table-wrapper"><table'
    | replace: '</table>', '</table></div>'
    | replace: '<code><div class="table-wrapper">', '<code>'
    | replace: '</table></div></code>', '</table></code>'
  %}
{% endif %}

<!--
  Fixed kramdown code highlight rendering:
  https://github.com/penibelst/jekyll-compress-html/issues/101
  https://github.com/penibelst/jekyll-compress-html/issues/71#issuecomment-188144901
-->

{% if _content contains '<pre class="highlight">' %}
  {% assign _content = _content
    | replace: '<div class="highlight"><pre class="highlight"><code', '<div class="highlight"><code'
    | replace: '</code></pre></div>', '</code></div>'
  %}
{% endif %}

<!-- Change the icon of checkbox -->

{% if _content contains '<input type="checkbox"' %}
  {% assign _content = _content
    | replace: '<input type="checkbox" class="task-list-item-checkbox" disabled="disabled" checked="checked" />',
      '<i class="fas fa-check-circle fa-fw checked"></i>'
    | replace: '<input type="checkbox" class="task-list-item-checkbox" disabled="disabled" />',
      '<i class="far fa-circle fa-fw"></i>'
  %}
{% endif %}

<!-- Handle images -->

{% assign IMG_TAG = '<img ' %}

{% if _content contains IMG_TAG %}
  {% assign _img_content = null %}
  {% assign _img_snippets = _content | split: IMG_TAG %}

  {% for _img_snippet in _img_snippets %}
    {% if forloop.first %}
      {% assign _img_content = _img_snippet %}
      {% continue %}
    {% endif %}

    {% assign _left = _img_snippet | split: '>' | first %}
    {% assign _right = _img_snippet | remove: _left %}

    {% unless _left contains 'src=' %}
      {% continue %}
    {% endunless %}

    {% assign _left = _left | remove: ' /' | replace: ' w=', ' width=' | replace: ' h=', ' height=' %}
    {% assign _attrs = _left | split: '" ' %}

    {% assign _src = null %}
    {% assign _lqip = null %}
    {% assign _class = null %}

    {% for _attr in _attrs %}
      {% unless _attr contains '=' %}
        {% continue %}
      {% endunless %}

      {% assign _pair = _attr | split: '="' %}
      {% capture _key %}{{ _pair | first }}{% endcapture %}
      {% capture _value %}{{ _pair | last | remove: '"' }}{% endcapture %}

      {% case _key %}
        {% when 'src' %}
          {% assign _src = _value %}
        {% when 'lqip' %}
          {% assign _lqip = _value %}
        {% when 'class' %}
          {% assign _class = _value %}
      {% endcase %}
    {% endfor %}

    <!-- take out classes -->
    {% assign _wrapper_class = '' %}

    {% if _class %}
      {% assign _remain = _class %}
      {% assign _class_array = _class | split: ' ' %}

      {% for c in _class_array %}
        {% assign _pick = false %}

        {% case c %}
          {% when 'preview-img', 'normal', 'left', 'right', 'light', 'dark' %}
            {% assign _pick = true %}
          {% else %}
            {% if c contains '-' %}
              {% assign start = c | split: '-' | first %}
              {% if start == 'w' %}
                {% assign _pick = true %}
              {% endif %}
            {% endif %}
        {% endcase %}

        {% if _pick %}
          {% assign _remain = _remain | remove: c | strip %}
          {% assign _wrapper_class = _wrapper_class | append: ' ' | append: c %}
        {% endif %}
      {% endfor %}

      {% unless _wrapper_class == '' %}
        {% capture _old_class %}class="{{ _class }}"{% endcapture %}
        {% assign _left = _left | remove: _old_class %}
        {% unless _remain == '' %}
          {% capture _new_class %}class="{{ _remain }}"{% endcapture %}
          {% assign _left = _left | append: _new_class %}
        {% endunless %}
      {% endunless %}
    {% endif %}

    {% assign _final_src = null %}
    {% assign _lazyload = true %}

    {%- capture _img_url -%}
      {% include media-url.html src=_src subpath=page.media_subpath %}
    {%- endcapture -%}

    {% assign _path_prefix = _img_url | remove: _src %}

    {% unless _src contains '//' %}
      {% assign _final_src = _path_prefix | append: _src %}
      {% assign _src_alt = 'src="' | append: _path_prefix %}
      {% assign _left = _left | replace: 'src="', _src_alt %}
    {% endunless %}

    {% if _lqip %}
      {% assign _lazyload = false %}
      {% assign _wrapper_class = _wrapper_class | append: ' blur' %}

      {% unless _lqip contains 'data:' %}
        {% assign _lqip_alt = 'lqip="' | append: _path_prefix %}
        {% assign _left = _left | replace: 'lqip="', _lqip_alt %}
      {% endunless %}

      <!-- add image placeholder -->
      {% assign _left = _left | replace: 'src=', 'data-src=' | replace: ' lqip=', ' data-lqip="true" src=' %}

    {% else %}
      {% assign _wrapper_class = _wrapper_class | append: ' shimmer' %}
    {% endif %}

    <!-- lazy-load images -->
    {% if _lazyload %}
      {% assign _left = _left | append: ' loading="lazy"' %}
    {% endif %}

    {% if page.layout == 'home' %}
      <!-- create the image wrapper -->
      {% assign _wrapper_start = '<div class="preview-img ' | append: _class | append: '">' %}

      {% assign _img_content = _img_content | append: _wrapper_start %}
      {% assign _right = _right | prepend: '></div' %}

    {% else %}
      <!-- make sure the `<img>` is wrapped by `<a>` -->
      {% assign _parent = _right | slice: 1, 4 %}

      {% if _parent == '</a>' %}
        <!-- add class to exist <a> tag -->
        {% assign _size = _img_content | size | minus: 1 %}
        {% capture _class %}
          class="img-link{% unless _lqip %} shimmer{% endunless %}"
        {% endcapture %}
        {% assign _img_content = _img_content | slice: 0, _size | append: _class | append: '>' %}

      {% else %}
        <!-- create the image wrapper -->
        {% assign _wrapper_start = _final_src
          | default: _src
          | prepend: '<a href="'
          | append: '" class="popup img-link'
          | append: _wrapper_class
          | append: '">'
        %}

        {% assign _img_content = _img_content | append: _wrapper_start %}
        {% assign _right = '></a' | append: _right %}
      {% endif %}
    {% endif %}

    <!-- combine -->
    {% assign _img_content = _img_content | append: IMG_TAG | append: _left | append: _right %}
  {% endfor %}

  {% if _img_content %}
    {% assign _content = _img_content %}
  {% endif %}
{% endif %}

<!-- Add header for code snippets -->

{% if _content contains '<div class="highlight"><code>' %}
  {% assign _code_spippets = _content | split: '<div class="highlight"><code>' %}
  {% assign _new_content = '' %}

  {% for _snippet in _code_spippets %}
    {% if forloop.last %}
      {% assign _new_content = _new_content | append: _snippet %}

    {% else %}
      {% assign _left = _snippet | split: '><' | last %}

      {% if _left contains 'file="' %}
        {% assign _label_text = _left | split: 'file="' | last | split: '"' | first %}
        {% assign _label_icon = 'far fa-file-code fa-fw' %}
      {% else %}
        {% assign _lang = _left | split: 'language-' | last | split: ' ' | first %}
        {% capture _label_text %}{% include language-alias.html language=_lang %}{% endcapture %}
        {% assign _label_icon = 'fas fa-code fa-fw small' %}
      {% endif %}

      {% capture _label %}
        <span data-label-text="{{ _label_text | strip }}"><i class="{{ _label_icon }}"></i></span>
      {% endcapture %}

      {% assign _new_content = _new_content
        | append: _snippet
        | append: '<div class="code-header">'
        | append: _label
        | append: '<button aria-label="copy" data-title-succeed="'
        | append: site.data.locales[include.lang].post.button.copy_code.succeed
        | append: '"><i class="far fa-clipboard"></i></button></div>'
        | append: '<div class="highlight"><code>'
      %}
    {% endif %}
  {% endfor %}

  {% assign _content = _new_content %}
{% endif %}

<!-- Create heading anchors -->

{% assign heading_levels = '2,3,4,5' | split: ',' %}
{% assign _heading_content = _content %}

{% for level in heading_levels %}
  {% assign mark_start = '<h' | append: level | append: ' id="' %}
  {% assign mark_end = '</h' | append: level | append: '>' %}

  {% if _heading_content contains mark_start %}
    {% assign _new_content = null %}
    {% assign heading_snippets = _heading_content | split: mark_start %}

    {% for snippet in heading_snippets %}
      {% if forloop.first %}
        {% assign _new_content = snippet %}
        {% continue %}
      {% endif %}

      {% assign id = snippet | split: '"' | first %}
      {% assign anchor = '<a href="#'
        | append: id
        | append: '" class="anchor text-muted"><i class="fas fa-hashtag"></i></a>'
      %}

      {% assign left = snippet | split: mark_end | first %}
      {% assign right = snippet | slice: left.size, snippet.size %}
      {% assign left = left | replace_first: '">', '"><span class="me-2">' | append: '</span>' %}

      {% assign _new_content = _new_content | append: mark_start | append: left | append: anchor | append: right %}
    {% endfor %}

    {% assign _heading_content = _new_content %}
  {% endif %}
{% endfor %}

{% assign _content = _heading_content %}

<!-- return -->
{{ _content }}
