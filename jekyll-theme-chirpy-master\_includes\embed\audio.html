{% assign src = include.src | strip %}
{% assign title = include.title | strip %}
{% assign types = include.types | default: '' | strip | split: '|' %}

{% unless src contains '://' %}
  {%- capture src -%}
    {% include media-url.html src=src subpath=page.media_subpath %}
  {%- endcapture -%}
{% endunless %}

<p>
  <audio class="embed-audio" controls>
    {% assign extension = src | split: '.' | last %}
    {% assign types = extension | concat: types %}

    {% assign ext_size = extension | size %}
    {% assign src_size = src | size %}
    {% assign slice_size = src_size | minus: ext_size %}

    {% assign filepath = src | slice: 0, slice_size %}

    {% for type in types %}
      {% assign src = filepath | append: type %}
      {% assign media_item = site.data.media | find: 'extension', type %}
      {% assign mime_type = media_item.mime_type | default: type %}
      <source src="{{ src }}" type="audio/{{ mime_type }}">
    {% endfor %}

    Your browser does not support the audio tag. Here is a
    <a href="{{ src | strip }}">link to the audio file</a> instead.
  </audio>
  {% if title %}
    <em>{{ title }}</em>
  {% endif %}
</p>
