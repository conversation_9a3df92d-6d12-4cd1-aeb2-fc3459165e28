name: CI

on:
  push:
    branches:
      - master
      - "hotfix/*"
    paths-ignore:
      - ".github/**"
      - "!.github/workflows/ci.yml"
      - .gitignore
      - "docs/**"
      - README.md
      - LICENSE
  pull_request:
    paths-ignore:
      - ".github/**"
      - "!.github/workflows/ci.yml"
      - .gitignore
      - "docs/**"
      - README.md
      - LICENSE

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        ruby: ["3.1", "3.2", "3.3"]

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # for posts's lastmod

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby }}
          bundler-cache: true

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Build Assets
        run: npm i && npm run build

      - name: Test Site
        run: bash tools/test.sh
