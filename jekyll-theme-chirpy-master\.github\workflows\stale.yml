name: "Close stale issues and PRs"

on:
  schedule:
    - cron: "0 0 * * *" # every day at 00:00 UTC

permissions:
  issues: write
  pull-requests: write

env:
  STALE_LABEL: inactive
  EXEMPT_LABELS: "pending,planning,in progress"
  MESSAGE: >
    This conversation has been automatically marked as stale because it has not had recent activity.
    It will be closed if no further activity occurs.
    Thank you for your contributions.

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          # 60 days before marking issues/PRs stale
          days-before-close: -1 # does not close automatically
          stale-issue-label: ${{ env.STALE_LABEL }}
          exempt-issue-labels: ${{ env.EXEMPT_LABELS }}
          stale-issue-message: ${{ env.MESSAGE }}
          stale-pr-label: ${{ env.STALE_LABEL }}
          exempt-pr-labels: ${{ env.EXEMPT_LABELS }}
          stale-pr-message: ${{ env.MESSAGE }}
