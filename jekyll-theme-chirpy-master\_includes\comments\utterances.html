<!-- https://utteranc.es/ -->
<script>
  (function () {
    const origin = 'https://utteranc.es';
    const themeMapper = Theme.getThemeMapper('github-light', 'github-dark');
    const initTheme = themeMapper[Theme.visualState];

    let script = document.createElement('script');
    script.src = 'https://utteranc.es/client.js';
    script.setAttribute('repo', '{{ site.comments.utterances.repo }}');
    script.setAttribute('issue-term', '{{ site.comments.utterances.issue_term }}');
    script.setAttribute('theme', initTheme);
    script.crossOrigin = 'anonymous';
    script.async = true;

    const $footer = document.querySelector('footer');
    $footer.insertAdjacentElement('beforebegin', script);

    addEventListener('message', (event) => {
      let newTheme;

      {%- comment -%}
        Credit to <https://github.com/utterance/utterances/issues/170#issuecomment-594036347>
      {%- endcomment -%}
      if (event.source === window && event.data && event.data.id === Theme.ID) {
        newTheme = themeMapper[Theme.visualState];

        const message = {
          type: 'set-theme',
          theme: newTheme
        };

        const utterances = document.querySelector('.utterances-frame').contentWindow;
        utterances.postMessage(message, origin);
      }
    });
  })();
</script>
