{
  "name": "<PERSON><PERSON><PERSON>",
  "image": "mcr.microsoft.com/devcontainers/jekyll:2-bullseye",
  "onCreateCommand": "git config --global --add safe.directory ${containerWorkspaceFolder}",
  "postCreateCommand": "bash .devcontainer/post-create.sh",
  "customizations": {
    "vscode": {
      "settings": {
        "terminal.integrated.defaultProfile.linux": "zsh"
      },
      "extensions": [
        // Liquid tags auto-complete
        "killalau.vscode-liquid-snippets",
        // Liquid syntax highlighting and formatting
        "Shopify.theme-check-vscode",
        // Shell
        "timonwong.shellcheck",
        "mkhl.shfmt",
        // Common formatter
        "EditorConfig.EditorConfig",
        "esbenp.prettier-vscode",
        "stylelint.vscode-stylelint",
        "yzhang.markdown-all-in-one",
        // Git
        "mhutchie.git-graph"
      ]
    }
  }
}
