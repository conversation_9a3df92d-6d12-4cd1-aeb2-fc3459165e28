{%- comment -%}
  Get post description or generate it from the post content.
{%- endcomment -%}

{%- assign max_length = include.max_length | default: 200 -%}

{%- capture description -%}
{%- if post.description -%}
  {{- post.description -}}
{%- else -%}
  {% comment %}
    Remove the line number of the code snippet.
  {% endcomment %}
  {% assign content = post.content %}

  {% if content contains '<td class="rouge-gutter gl"><pre class="lineno">' %}
    {% assign content = content | replace: '<td class="rouge-gutter gl"><pre class="lineno">', '<!-- <td class="rouge-gutter gl"><pre class="lineno">'%}
    {% assign content = content | replace: '</td><td class="rouge-code">', '</td> --><td class="rouge-code">' %}
  {% endif %}

  {{- content | markdownify | strip_html | newline_to_br | replace: '<br />', ' ' | strip_newlines -}}
{%- endif -%}
{%- endcapture -%}

{{- description | strip | truncate: max_length | escape -}}
