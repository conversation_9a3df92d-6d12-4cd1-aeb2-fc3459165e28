{% assign urls = include.urls | split: ',' %}

{% assign combined_urls = null %}

{% assign domain = 'https://cdn.jsdelivr.net/' %}

{% for url in urls %}
  {% if url contains domain %}
    {% assign url_snippet = url | slice: domain.size, url.size %}

    {% if combined_urls %}
      {% assign combined_urls = combined_urls | append: ',' | append: url_snippet %}
    {% else %}
      {% assign combined_urls = domain | append: 'combine/' | append: url_snippet %}
    {% endif %}

  {% elsif url contains '//' %}
    <script defer src="{{ url }}"></script>
  {% else %}
    <script defer src="{{ url | relative_url }}"></script>
  {% endif %}
{% endfor %}

{% if combined_urls %}
  <script defer src="{{ combined_urls }}"></script>
{% endif %}
