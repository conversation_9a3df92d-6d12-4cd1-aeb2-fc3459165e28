{% assign video_url = include.src %}
{% assign title = include.title %}
{% assign poster_url = include.poster %}
{% assign types = include.types | default: '' | strip | split: '|' %}

{% unless video_url contains '://' %}
  {%- capture video_url -%}
    {% include media-url.html src=video_url subpath=page.media_subpath %}
  {%- endcapture -%}
{% endunless %}

{% if poster_url %}
  {% unless poster_url contains '://' %}
    {%- capture poster_url -%}
      {% include media-url.html src=poster_url subpath=page.media_subpath %}
    {%- endcapture -%}
  {% endunless %}
  {% assign poster = 'poster="' | append: poster_url | append: '"' %}
{% endif %}

{% assign attributes = 'controls' %}

{% if include.autoplay %}
  {% assign attributes = attributes | append: ' ' | append: 'autoplay' %}
{% endif %}

{% if include.loop %}
  {% assign attributes = attributes | append: ' ' | append: 'loop' %}
{% endif %}

{% if include.muted %}
  {% assign attributes = attributes | append: ' ' | append: 'muted' %}
{% endif %}

<p>
  <video class="embed-video file" {{ poster }} {{ attributes }}>
    {% assign extension = video_url | split: '.' | last %}
    {% assign types = extension | concat: types %}

    {% assign ext_size = extension | size %}
    {% assign src_size = video_url | size %}
    {% assign slice_size = src_size | minus: ext_size %}

    {% assign filepath = video_url | slice: 0, slice_size %}

    {% for type in types %}
      {% assign src = filepath | append: type %}
      {% assign media_item = site.data.media | find: 'extension', type %}
      {% assign mime_type = media_item.mime_type | default: type %}
      <source src="{{ src }}" type="video/{{ mime_type }}">
    {% endfor %}

    Your browser does not support the video tag. Here is a
    <a href="{{ video_url | strip }}">link to the video file</a> instead.
  </video>
  {% if title %}
    <em>{{ title }}</em>
  {% endif %}
</p>
