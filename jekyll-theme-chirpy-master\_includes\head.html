<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="theme-color" media="(prefers-color-scheme: light)" content="#f7f7f7">
  <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#1b1b1e">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta
    name="viewport"
    content="width=device-width, user-scalable=no initial-scale=1, shrink-to-fit=no, viewport-fit=cover"
  >

  {%- capture seo_tags -%}
    {% seo title=false %}
  {%- endcapture -%}

  <!-- Setup Open Graph image -->

  {% if page.image %}
    {% assign src = page.image.path | default: page.image %}

    {% unless src contains '://' %}
      {%- capture img_url -%}
        {% include media-url.html src=src subpath=page.media_subpath absolute=true %}
      {%- endcapture -%}

      {%- capture old_url -%}{{ src | absolute_url }}{%- endcapture -%}
      {%- capture new_url -%}{{ img_url }}{%- endcapture -%}

      {% assign seo_tags = seo_tags | replace: old_url, new_url %}
    {% endunless %}

  {% elsif site.social_preview_image %}
    {%- capture img_url -%}
      {% include media-url.html src=site.social_preview_image absolute=true %}
    {%- endcapture -%}

    {%- capture og_image -%}
      <meta property="og:image" content="{{ img_url }}" />
    {%- endcapture -%}

    {%- capture twitter_image -%}
      <meta name="twitter:card" content="summary_large_image" />
      <meta property="twitter:image" content="{{ img_url }}" />
    {%- endcapture -%}

    {% assign old_meta_clip = '<meta name="twitter:card" content="summary" />' %}
    {% assign new_meta_clip = og_image | append: twitter_image %}
    {% assign seo_tags = seo_tags | replace: old_meta_clip, new_meta_clip %}
  {% endif %}

  {{ seo_tags }}

  <title>
    {%- unless page.layout == 'home' -%}
      {{ page.title | append: ' | ' }}
    {%- endunless -%}
    {{ site.title }}
  </title>

  {% include_cached favicons.html %}

  <!-- Resource Hints -->
  {% unless site.assets.self_host.enabled %}
    {% for hint in site.data.origin.cors.resource_hints %}
      {% for link in hint.links %}
        <link rel="{{ link.rel }}" href="{{ hint.url }}" {{ link.opts | join: ' ' }}>
      {% endfor %}
    {% endfor %}
  {% endunless %}

  <!-- Bootstrap -->
  {% unless jekyll.environment == 'production' %}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css">
  {% endunless %}

  <!-- Theme style -->
  <link rel="stylesheet" href="{{ '/assets/css/:THEME.css' | replace: ':THEME', site.theme | relative_url }}">

  <!-- Web Font -->
  <link rel="stylesheet" href="{{ site.data.origin[type].webfonts | relative_url }}">

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="{{ site.data.origin[type].fontawesome.css | relative_url }}">

  <!-- 3rd-party Dependencies -->

  {% if site.toc and page.toc %}
    <link rel="stylesheet" href="{{ site.data.origin[type].toc.css | relative_url }}">
  {% endif %}

  {% if page.layout == 'post' or page.layout == 'page' or page.layout == 'home' %}
    <link rel="stylesheet" href="{{ site.data.origin[type]['lazy-polyfill'].css | relative_url }}">
  {% endif %}

  {% if page.layout == 'page' or page.layout == 'post' %}
    <!-- Image Popup -->
    <link rel="stylesheet" href="{{ site.data.origin[type].glightbox.css | relative_url }}">
  {% endif %}

  <!-- Scripts -->

  <script src="{{ '/assets/js/dist/theme.min.js' | relative_url }}"></script>

  {% include js-selector.html lang=lang %}

  {% if jekyll.environment == 'production' %}
    <!-- PWA -->
    {% if site.pwa.enabled %}
      <script
        defer
        src="{{ '/app.min.js' | relative_url }}?baseurl={{ site.baseurl | default: '' }}&register={{ site.pwa.cache.enabled }}"
      ></script>
    {% endif %}

    <!-- Web Analytics -->
    {% for analytics in site.analytics %}
      {% capture str %}{{ analytics }}{% endcapture %}
      {% assign platform = str | split: '{' | first %}
      {% if site.analytics[platform].id and site.analytics[platform].id != empty %}
        {% include analytics/{{ platform }}.html %}
      {% endif %}
    {% endfor %}
  {% endif %}

  {% include metadata-hook.html %}
</head>
